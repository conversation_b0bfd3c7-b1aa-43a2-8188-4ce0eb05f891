name: Build

on:
  pull_request:
  push:
  repository_dispatch:
  release:
    types:
      - created

jobs:
  set-matrix:
    runs-on: ubuntu-latest
    outputs:
      matrix: ${{ steps.set-matrix.outputs.matrix }}
    steps:
      - name: Checkout Code
        uses: actions/checkout@v4

      - name: Set matrix
        id: set-matrix
        working-directory: src/boards
        run: |
          MATRIX_JSON=$(ls -d */ | sed 's/\/$//' | jq -R -s -c 'split("\n")[:-1]')
          echo "matrix=$MATRIX_JSON"
          echo "matrix=$MATRIX_JSON" >> $GITHUB_OUTPUT

  build:
    needs: set-matrix
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        board: ${{ fromJSON(needs.set-matrix.outputs.matrix) }}
    steps:
    - name: Checkout Code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0
        submodules: true

    - name: Install ARM GCC
      uses: carlosperate/arm-none-eabi-gcc-action@v1
      with:
        release: '12.3.Rel1'

    - name: Install Tools
      run: |
        pip3 install adafruit-nrfutil uritemplate requests intelhex setuptools

    - name: Build
      run: |
        make BOARD=${{ matrix.board }} all
        make BOARD=${{ matrix.board }} copy-artifact

    - uses: actions/upload-artifact@v4
      with:
        name: ${{ matrix.board }}
        path: _bin/${{ matrix.board }}

    - name: Upload Release Asset
      uses: softprops/action-gh-release@v1
      if: ${{ github.event_name == 'release' }}
      with:
        files: |
          _bin/${{ matrix.board }}/${{ matrix.board }}_bootloader-*.zip
          _bin/${{ matrix.board }}/${{ matrix.board }}_bootloader-*.hex
          _bin/${{ matrix.board }}/update-${{ matrix.board }}_bootloader-*.uf2
